#!/usr/bin/env python3
"""
Simple Single Table Schema Generator
Creates one table that directly mirrors the CSV structure.
"""

import logging
from database_connection import DatabaseConnection

class SimpleSingleTableSchema:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_connection = DatabaseConnection()
        
        # Establish database connection
        if not self.db_connection.connect():
            raise Exception("Failed to connect to database")
    
    def create_schema(self):
        """Create a single table that mirrors the CSV structure."""
        
        # Drop existing table if it exists
        drop_sql = """
        DROP TABLE IF EXISTS "icd11_terms" CASCADE;
        """
        
        # Create single table with all CSV columns
        create_table_sql = """
        CREATE TABLE "icd11_terms" (
            "id" SERIAL PRIMARY KEY,
            "chapter_code" VARCHAR(50) NOT NULL,
            "term_code" VARCHAR(100) NOT NULL,
            "en" TEXT,
            "es" TEXT,
            "fr" TEXT,
            "ar" TEXT,
            "de" TEXT,
            "uk" TEXT,
            "en_normalized" TEXT,
            "es_normalized" TEXT,
            "fr_normalized" TEXT,
            "ar_normalized" TEXT,
            "de_normalized" TEXT,
            "uk_normalized" TEXT,
            "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        # Create indexes for better performance
        indexes_sql = [
            'CREATE INDEX "idx_icd11_terms_chapter_code" ON "icd11_terms" ("chapter_code");',
            'CREATE INDEX "idx_icd11_terms_term_code" ON "icd11_terms" ("term_code");',
            'CREATE UNIQUE INDEX "idx_icd11_terms_unique" ON "icd11_terms" ("chapter_code", "term_code");',
            'CREATE INDEX "idx_icd11_terms_en" ON "icd11_terms" USING gin(to_tsvector(\'english\', "en"));',
            'CREATE INDEX "idx_icd11_terms_es" ON "icd11_terms" USING gin(to_tsvector(\'spanish\', "es"));',
            'CREATE INDEX "idx_icd11_terms_fr" ON "icd11_terms" USING gin(to_tsvector(\'french\', "fr"));'
        ]
        
        # Create trigger for updated_at
        trigger_sql = """
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        
        CREATE TRIGGER update_icd11_terms_updated_at 
            BEFORE UPDATE ON "icd11_terms" 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        """
        
        try:
            # Drop existing table
            self.db_connection.execute_command(drop_sql)
            self.logger.info("Dropped existing table if it existed")
            
            # Create table
            self.db_connection.execute_command(create_table_sql)
            self.logger.info("Created icd11_terms table")
            
            # Create indexes
            for index_sql in indexes_sql:
                try:
                    self.db_connection.execute_command(index_sql)
                    self.logger.info(f"Created index successfully")
                except Exception as e:
                    self.logger.warning(f"Index creation failed (non-critical): {e}")
            
            # Create trigger
            self.db_connection.execute_command(trigger_sql)
            self.logger.info("Created update trigger")
            
            self.logger.info("Single table schema created successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Schema creation failed: {e}")
            return False
    
    def get_table_info(self):
        """Get information about the created table."""
        try:
            # Get column information
            columns_sql = """
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'icd11_terms' 
            ORDER BY ordinal_position;
            """
            
            columns = self.db_connection.execute_query(columns_sql)
            
            # Get index information
            indexes_sql = """
            SELECT indexname, indexdef 
            FROM pg_indexes 
            WHERE tablename = 'icd11_terms';
            """
            
            indexes = self.db_connection.execute_query(indexes_sql)
            
            return {
                'columns': columns,
                'indexes': indexes
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get table info: {e}")
            return None

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(levelname)s:%(name)s:%(message)s'
    )
    
    print("=== Creating Simple Single Table Schema ===")
    
    try:
        # Create schema
        schema_generator = SimpleSingleTableSchema()
        
        if schema_generator.create_schema():
            print("✅ Single table schema created successfully!")
            
            # Show table information
            info = schema_generator.get_table_info()
            if info:
                print(f"\n📊 Table Structure:")
                print(f"Columns: {len(info['columns'])}")
                for col in info['columns']:
                    nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                    print(f"  - {col['column_name']}: {col['data_type']} ({nullable})")
                
                print(f"\nIndexes: {len(info['indexes'])}")
                for idx in info['indexes']:
                    print(f"  - {idx['indexname']}")
        else:
            print("❌ Schema creation failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
