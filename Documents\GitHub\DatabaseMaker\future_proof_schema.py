#!/usr/bin/env python3
"""
Future-proof schema generator for ICD-11 multilingual terminology data.
Creates a normalized, scalable database schema that can handle new languages,
versions, and evolving requirements.
"""

from database_connection import DatabaseConnection
from typing import Dict, List, Optional
import logging

class FutureProofSchemaGenerator:
    """
    Generates a future-proof, normalized schema for multilingual terminology data.
    """
    
    def __init__(self, db_config: Optional[Dict[str, str]] = None):
        """Initialize schema generator with database connection."""
        self.db_connection = DatabaseConnection(db_config)
        self.logger = logging.getLogger(__name__)
        # Ensure connection is established
        if not self.db_connection.connect():
            raise Exception("Failed to connect to database")
    
    def create_future_proof_schema(self, drop_if_exists: bool = False) -> Dict[str, bool]:
        """
        Create a complete future-proof schema for ICD-11 terminology.
        
        Args:
            drop_if_exists: Whether to drop existing tables
            
        Returns:
            Dictionary with creation status for each table
        """
        results = {}
        
        try:
            # Drop tables in reverse dependency order if requested
            if drop_if_exists:
                self._drop_existing_tables()
            
            # Create tables in dependency order
            results['languages'] = self._create_languages_table()
            results['chapters'] = self._create_chapters_table()
            results['terms'] = self._create_terms_table()
            results['term_translations'] = self._create_term_translations_table()
            results['term_normalizations'] = self._create_term_normalizations_table()
            results['schema_metadata'] = self._create_schema_metadata_table()
            
            # Create indexes for performance
            results['indexes'] = self._create_performance_indexes()
            
            # Create views for easy querying
            results['views'] = self._create_convenience_views()
            
            # Insert initial data
            results['initial_data'] = self._insert_initial_data()
            
            self.logger.info("Future-proof schema created successfully")
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to create schema: {e}")
            raise
    
    def _drop_existing_tables(self):
        """Drop existing tables in correct order."""
        tables_to_drop = [
            'term_normalizations',
            'term_translations', 
            'terms',
            'chapters',
            'languages',
            'schema_metadata'
        ]
        
        for table in tables_to_drop:
            try:
                self.db_connection.execute_command(f'DROP TABLE IF EXISTS "{table}" CASCADE;')
            except Exception as e:
                self.logger.warning(f"Could not drop table {table}: {e}")
    
    def _create_languages_table(self) -> bool:
        """Create languages reference table."""
        sql = """
        CREATE TABLE IF NOT EXISTS "languages" (
            "language_id" SERIAL PRIMARY KEY,
            "language_code" VARCHAR(10) NOT NULL UNIQUE,
            "language_name" VARCHAR(100) NOT NULL,
            "iso_639_1" CHAR(2),
            "iso_639_3" CHAR(3),
            "is_active" BOOLEAN DEFAULT TRUE,
            "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        COMMENT ON TABLE "languages" IS 'Reference table for supported languages';
        COMMENT ON COLUMN "languages"."language_code" IS 'Internal language code (en, es, fr, etc.)';
        COMMENT ON COLUMN "languages"."iso_639_1" IS 'ISO 639-1 two-letter language code';
        COMMENT ON COLUMN "languages"."iso_639_3" IS 'ISO 639-3 three-letter language code';
        """
        
        try:
            self.db_connection.execute_command(sql)
            return True
        except Exception as e:
            self.logger.error(f"Failed to create languages table: {e}")
            return False
    
    def _create_chapters_table(self) -> bool:
        """Create chapters table for ICD-11 chapters."""
        sql = """
        CREATE TABLE IF NOT EXISTS "chapters" (
            "chapter_id" SERIAL PRIMARY KEY,
            "chapter_code" VARCHAR(20) NOT NULL UNIQUE,
            "chapter_number" INTEGER,
            "is_active" BOOLEAN DEFAULT TRUE,
            "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        COMMENT ON TABLE "chapters" IS 'ICD-11 chapters reference table';
        COMMENT ON COLUMN "chapters"."chapter_code" IS 'ICD-11 chapter code (e.g., TECH001, MED001)';
        """
        
        try:
            self.db_connection.execute_command(sql)
            return True
        except Exception as e:
            self.logger.error(f"Failed to create chapters table: {e}")
            return False
    
    def _create_terms_table(self) -> bool:
        """Create main terms table."""
        sql = """
        CREATE TABLE IF NOT EXISTS "terms" (
            "term_id" SERIAL PRIMARY KEY,
            "term_code" VARCHAR(20) NOT NULL,
            "chapter_id" INTEGER NOT NULL REFERENCES "chapters"("chapter_id"),
            "icd11_code" VARCHAR(50),
            "term_version" INTEGER DEFAULT 1,
            "is_active" BOOLEAN DEFAULT TRUE,
            "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            UNIQUE("term_code", "chapter_id", "term_version")
        );
        
        COMMENT ON TABLE "terms" IS 'Main terms table with versioning support';
        COMMENT ON COLUMN "terms"."term_code" IS 'Term code within chapter';
        COMMENT ON COLUMN "terms"."term_version" IS 'Version number for term evolution';
        COMMENT ON COLUMN "terms"."icd11_code" IS 'Official ICD-11 code if available';
        """
        
        try:
            self.db_connection.execute_command(sql)
            return True
        except Exception as e:
            self.logger.error(f"Failed to create terms table: {e}")
            return False
    
    def _create_term_translations_table(self) -> bool:
        """Create term translations table."""
        sql = """
        CREATE TABLE IF NOT EXISTS "term_translations" (
            "translation_id" SERIAL PRIMARY KEY,
            "term_id" INTEGER NOT NULL REFERENCES "terms"("term_id") ON DELETE CASCADE,
            "language_id" INTEGER NOT NULL REFERENCES "languages"("language_id"),
            "translation_text" TEXT NOT NULL,
            "translation_quality" VARCHAR(20) DEFAULT 'standard',
            "is_primary" BOOLEAN DEFAULT TRUE,
            "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            UNIQUE("term_id", "language_id", "is_primary")
        );
        
        COMMENT ON TABLE "term_translations" IS 'Translations of terms in different languages';
        COMMENT ON COLUMN "term_translations"."translation_quality" IS 'Quality indicator: draft, standard, verified, official';
        COMMENT ON COLUMN "term_translations"."is_primary" IS 'Whether this is the primary translation for this language';
        """
        
        try:
            self.db_connection.execute_command(sql)
            return True
        except Exception as e:
            self.logger.error(f"Failed to create term_translations table: {e}")
            return False
    
    def _create_term_normalizations_table(self) -> bool:
        """Create term normalizations table."""
        sql = """
        CREATE TABLE IF NOT EXISTS "term_normalizations" (
            "normalization_id" SERIAL PRIMARY KEY,
            "translation_id" INTEGER NOT NULL REFERENCES "term_translations"("translation_id") ON DELETE CASCADE,
            "normalized_text" TEXT NOT NULL,
            "normalization_type" VARCHAR(50) DEFAULT 'standard',
            "normalization_rules" JSONB,
            "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        COMMENT ON TABLE "term_normalizations" IS 'Normalized versions of translations for search optimization';
        COMMENT ON COLUMN "term_normalizations"."normalization_type" IS 'Type of normalization: lowercase, stemmed, phonetic, etc.';
        COMMENT ON COLUMN "term_normalizations"."normalization_rules" IS 'JSON metadata about normalization rules applied';
        """
        
        try:
            self.db_connection.execute_command(sql)
            return True
        except Exception as e:
            self.logger.error(f"Failed to create term_normalizations table: {e}")
            return False
    
    def _create_schema_metadata_table(self) -> bool:
        """Create schema metadata table for versioning and tracking."""
        sql = """
        CREATE TABLE IF NOT EXISTS "schema_metadata" (
            "metadata_id" SERIAL PRIMARY KEY,
            "schema_version" VARCHAR(20) NOT NULL,
            "icd11_version" VARCHAR(20),
            "data_source" VARCHAR(200),
            "import_date" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            "record_count" INTEGER,
            "checksum" VARCHAR(64),
            "notes" TEXT
        );
        
        COMMENT ON TABLE "schema_metadata" IS 'Metadata about schema and data versions';
        """
        
        try:
            self.db_connection.execute_command(sql)
            return True
        except Exception as e:
            self.logger.error(f"Failed to create schema_metadata table: {e}")
            return False
    
    def _create_performance_indexes(self) -> bool:
        """Create indexes for optimal performance."""
        indexes = [
            # Primary lookup indexes
            'CREATE INDEX IF NOT EXISTS "idx_terms_chapter_code" ON "terms"("chapter_id", "term_code");',
            'CREATE INDEX IF NOT EXISTS "idx_terms_active" ON "terms"("is_active") WHERE "is_active" = TRUE;',
            
            # Translation lookup indexes
            'CREATE INDEX IF NOT EXISTS "idx_translations_term_lang" ON "term_translations"("term_id", "language_id");',
            'CREATE INDEX IF NOT EXISTS "idx_translations_language" ON "term_translations"("language_id");',
            'CREATE INDEX IF NOT EXISTS "idx_translations_primary" ON "term_translations"("language_id") WHERE "is_primary" = TRUE;',
            
            # Full-text search indexes
            'CREATE INDEX IF NOT EXISTS "idx_translations_text_gin" ON "term_translations" USING gin(to_tsvector(\'english\', "translation_text"));',
            'CREATE INDEX IF NOT EXISTS "idx_normalizations_text_gin" ON "term_normalizations" USING gin(to_tsvector(\'english\', "normalized_text"));',
            
            # Trigram indexes for fuzzy search (if pg_trgm extension is available)
            'CREATE INDEX IF NOT EXISTS "idx_translations_text_trgm" ON "term_translations" USING gin("translation_text" gin_trgm_ops);',
            'CREATE INDEX IF NOT EXISTS "idx_normalizations_text_trgm" ON "term_normalizations" USING gin("normalized_text" gin_trgm_ops);',
            
            # Composite indexes for common queries
            'CREATE INDEX IF NOT EXISTS "idx_terms_chapter_active" ON "terms"("chapter_id", "is_active");',
            'CREATE INDEX IF NOT EXISTS "idx_translations_term_primary" ON "term_translations"("term_id", "is_primary");'
        ]
        
        success_count = 0
        for index_sql in indexes:
            try:
                self.db_connection.execute_command(index_sql)
                success_count += 1
            except Exception as e:
                self.logger.warning(f"Could not create index: {e}")
        
        return success_count > 0
    
    def _create_convenience_views(self) -> bool:
        """Create views for easy querying."""
        views = [
            # Complete term view with all translations
            """
            CREATE OR REPLACE VIEW "v_terms_complete" AS
            SELECT 
                t.term_id,
                t.term_code,
                c.chapter_code,
                t.icd11_code,
                t.term_version,
                l.language_code,
                l.language_name,
                tr.translation_text,
                tr.is_primary,
                n.normalized_text,
                t.is_active
            FROM "terms" t
            JOIN "chapters" c ON t.chapter_id = c.chapter_id
            JOIN "term_translations" tr ON t.term_id = tr.term_id
            JOIN "languages" l ON tr.language_id = l.language_id
            LEFT JOIN "term_normalizations" n ON tr.translation_id = n.translation_id
            WHERE t.is_active = TRUE AND l.is_active = TRUE;
            """,
            
            # Primary translations only view
            """
            CREATE OR REPLACE VIEW "v_terms_primary" AS
            SELECT 
                t.term_id,
                t.term_code,
                c.chapter_code,
                t.icd11_code,
                l.language_code,
                tr.translation_text,
                n.normalized_text
            FROM "terms" t
            JOIN "chapters" c ON t.chapter_id = c.chapter_id
            JOIN "term_translations" tr ON t.term_id = tr.term_id AND tr.is_primary = TRUE
            JOIN "languages" l ON tr.language_id = l.language_id
            LEFT JOIN "term_normalizations" n ON tr.translation_id = n.translation_id
            WHERE t.is_active = TRUE AND l.is_active = TRUE;
            """
        ]
        
        success_count = 0
        for view_sql in views:
            try:
                self.db_connection.execute_command(view_sql)
                success_count += 1
            except Exception as e:
                self.logger.warning(f"Could not create view: {e}")
        
        return success_count > 0
    
    def _insert_initial_data(self) -> bool:
        """Insert initial reference data."""
        try:
            # Insert supported languages
            languages_data = [
                ('en', 'English', 'en', 'eng'),
                ('es', 'Spanish', 'es', 'spa'),
                ('fr', 'French', 'fr', 'fra'),
                ('ar', 'Arabic', 'ar', 'ara'),
                ('de', 'German', 'de', 'deu'),
                ('uk', 'Ukrainian', 'uk', 'ukr')
            ]
            
            for lang_code, lang_name, iso_639_1, iso_639_3 in languages_data:
                sql = """
                INSERT INTO "languages" ("language_code", "language_name", "iso_639_1", "iso_639_3")
                VALUES (%s, %s, %s, %s)
                ON CONFLICT ("language_code") DO NOTHING;
                """
                self.db_connection.execute_command(sql, (lang_code, lang_name, iso_639_1, iso_639_3))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to insert initial data: {e}")
            return False


def main():
    """Example usage of future-proof schema generator."""
    print("=== Future-Proof Schema Generator ===")
    
    try:
        # Initialize generator
        generator = FutureProofSchemaGenerator()
        
        # Create schema
        print("Creating future-proof schema...")
        results = generator.create_future_proof_schema(drop_if_exists=True)
        
        print("\nSchema Creation Results:")
        for component, success in results.items():
            status = "✓" if success else "✗"
            print(f"  {status} {component}")
        
        print("\n=== Schema Benefits ===")
        print("• Normalized structure prevents data duplication")
        print("• Supports unlimited languages and translations")
        print("• Version tracking for term evolution")
        print("• Optimized indexes for fast search")
        print("• Flexible normalization strategies")
        print("• Easy querying with convenience views")
        print("• Future-proof for new requirements")
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
