#!/usr/bin/env python3
"""
Simple CSV Loader
Loads CSV data directly into a single table.
"""

import pandas as pd
import logging
from database_connection import DatabaseConnection

class SimpleCSVLoader:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_connection = DatabaseConnection()
        
        # Establish database connection
        if not self.db_connection.connect():
            raise Exception("Failed to connect to database")
    
    def load_csv_to_table(self, csv_file_path):
        """Load CSV data directly into the icd11_terms table."""
        
        try:
            # Read CSV file
            print(f"Loading CSV file: {csv_file_path}")
            df = pd.read_csv(csv_file_path)
            
            print(f"Loaded {len(df)} rows from CSV")
            print(f"CSV columns: {list(df.columns)}")
            
            # Validate expected columns
            expected_columns = [
                'chapter_code', 'term_code', 'en', 'es', 'fr', 'ar', 'de', 'uk',
                'en_normalized', 'es_normalized', 'fr_normalized', 'ar_normalized', 
                'de_normalized', 'uk_normalized'
            ]
            
            missing_columns = [col for col in expected_columns if col not in df.columns]
            if missing_columns:
                raise Exception(f"Missing required columns: {missing_columns}")
            
            # Clear existing data
            clear_sql = 'DELETE FROM "icd11_terms";'
            self.db_connection.execute_command(clear_sql)
            print("Cleared existing data from table")
            
            # Insert data row by row
            insert_sql = """
            INSERT INTO "icd11_terms" (
                "chapter_code", "term_code", "en", "es", "fr", "ar", "de", "uk",
                "en_normalized", "es_normalized", "fr_normalized", "ar_normalized", 
                "de_normalized", "uk_normalized"
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            );
            """
            
            inserted_count = 0
            for index, row in df.iterrows():
                try:
                    # Prepare values, converting NaN to None
                    values = []
                    for col in expected_columns:
                        value = row[col]
                        if pd.isna(value):
                            values.append(None)
                        else:
                            values.append(str(value).strip())
                    
                    # Execute insert
                    self.db_connection.execute_command(insert_sql, tuple(values))
                    inserted_count += 1
                    
                    if inserted_count % 50 == 0:
                        print(f"Inserted {inserted_count} rows...")
                        
                except Exception as e:
                    self.logger.warning(f"Failed to insert row {index}: {e}")
                    continue
            
            print(f"✅ Successfully inserted {inserted_count} rows into icd11_terms table")
            
            # Verify the data
            count_sql = 'SELECT COUNT(*) FROM "icd11_terms";'
            result = self.db_connection.execute_query(count_sql)
            final_count = result[0]['count']
            
            print(f"✅ Final table count: {final_count} records")
            
            return True
            
        except Exception as e:
            self.logger.error(f"CSV loading failed: {e}")
            print(f"❌ Error loading CSV: {e}")
            return False
    
    def show_sample_data(self, limit=5):
        """Show sample data from the table."""
        try:
            sample_sql = f"""
            SELECT "chapter_code", "term_code", "en", "es", "fr" 
            FROM "icd11_terms" 
            LIMIT {limit};
            """
            
            results = self.db_connection.execute_query(sample_sql)
            
            print(f"\n📋 Sample Data (first {limit} rows):")
            for row in results:
                chapter = row['chapter_code']
                term = row['term_code']
                en = row['en'][:50] + "..." if row['en'] and len(row['en']) > 50 else row['en']
                es = row['es'][:50] + "..." if row['es'] and len(row['es']) > 50 else row['es']
                fr = row['fr'][:50] + "..." if row['fr'] and len(row['fr']) > 50 else row['fr']
                
                print(f"  {chapter}.{term}")
                print(f"    EN: {en}")
                print(f"    ES: {es}")
                print(f"    FR: {fr}")
                print()
                
        except Exception as e:
            self.logger.error(f"Failed to show sample data: {e}")

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(levelname)s:%(name)s:%(message)s'
    )
    
    print("=== Simple CSV to Single Table Loader ===")
    
    # CSV file path
    csv_file = "icd11_multilingual_terms_normalized_6lang - icd11_multilingual_terms_normalized_6lang.csv"
    
    try:
        loader = SimpleCSVLoader()
        
        if loader.load_csv_to_table(csv_file):
            loader.show_sample_data()
        else:
            print("❌ CSV loading failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
