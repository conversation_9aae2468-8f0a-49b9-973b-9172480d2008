"""
Improved CSV loader for the normalized schema.
Loads CSV data into the properly normalized database structure.
"""

import pandas as pd
import logging
from typing import Dict, Any, Optional, List
from improved_schema import ImprovedSchemaManager
import os

logger = logging.getLogger(__name__)


class ImprovedCSVLoader:
    """
    Loads CSV files into the improved normalized schema.
    """
    
    def __init__(self, db_config: Optional[Dict[str, str]] = None):
        """Initialize CSV loader with database connection."""
        self.schema_manager = ImprovedSchemaManager(db_config)
        self.supported_languages = ['en', 'es', 'fr', 'ar', 'de', 'uk']
    
    def load_csv_to_database(self, csv_file_path: str, 
                           create_schema: bool = True,
                           drop_existing: bool = False) -> Dict[str, Any]:
        """
        Load CSV file into database using improved normalized schema.
        
        Args:
            csv_file_path: Path to CSV file
            create_schema: Whether to create schema if it doesn't exist
            drop_existing: Whether to drop existing schema before creating
            
        Returns:
            Dictionary with loading results
        """
        results = {
            'success': False,
            'records_processed': 0,
            'records_inserted': 0,
            'chapters_created': 0,
            'translations_created': 0,
            'errors': [],
            'file_path': csv_file_path
        }
        
        try:
            # Validate file exists
            if not os.path.exists(csv_file_path):
                raise FileNotFoundError(f"CSV file not found: {csv_file_path}")
            
            # Create schema if requested
            if create_schema:
                logger.info("Creating database schema...")
                schema_results = self.schema_manager.create_schema(drop_if_exists=drop_existing)
                if 'error' in schema_results:
                    raise Exception(f"Failed to create schema: {schema_results['error']}")
                logger.info("✓ Database schema ready")
            
            # Load and process CSV
            logger.info(f"Loading CSV file: {csv_file_path}")
            df = pd.read_csv(csv_file_path)
            
            # Validate CSV structure
            if not self._validate_csv_structure(df):
                raise ValueError("CSV file does not match expected structure")
            
            logger.info(f"Found {len(df)} records in CSV")
            results['records_processed'] = len(df)
            
            # Process each row
            inserted_count = 0
            chapters_created = set()
            translations_count = 0
            
            for index, row in df.iterrows():
                try:
                    # Transform row to normalized schema format
                    chapter_code, term_code, translations = self._transform_row_to_normalized(row)

                    # Track chapters
                    chapters_created.add(chapter_code)

                    # Insert into database
                    if self.schema_manager.insert_term_data(chapter_code, term_code, translations):
                        inserted_count += 1
                        translations_count += len([t for t in translations.values() if t.get('raw')])
                    else:
                        results['errors'].append(f"Failed to insert row {index + 1}")
                        
                except Exception as e:
                    error_msg = f"Error processing row {index + 1}: {e}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
            
            results['records_inserted'] = inserted_count
            results['chapters_created'] = len(chapters_created)
            results['translations_created'] = translations_count
            results['success'] = inserted_count > 0
            
            # Log summary
            logger.info(f"✓ Processing complete:")
            logger.info(f"  Records processed: {results['records_processed']}")
            logger.info(f"  Records inserted: {results['records_inserted']}")
            logger.info(f"  Chapters created: {results['chapters_created']}")
            logger.info(f"  Translations created: {results['translations_created']}")
            logger.info(f"  Errors: {len(results['errors'])}")
            
            return results
            
        except Exception as e:
            error_msg = f"Failed to load CSV: {e}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
            return results
    
    def _validate_csv_structure(self, df: pd.DataFrame) -> bool:
        """Validate that CSV has expected columns."""
        required_columns = ['chapter_code', 'term_code']
        
        # Check for required columns
        for col in required_columns:
            if col not in df.columns:
                logger.error(f"Missing required column: {col}")
                return False
        
        # Check for language columns (at least some should exist)
        language_columns = []
        normalized_columns = []
        
        for lang in self.supported_languages:
            if lang in df.columns:
                language_columns.append(lang)
            
            normalized_col = f"{lang}_normalized"
            if normalized_col in df.columns:
                normalized_columns.append(normalized_col)
        
        if not language_columns:
            logger.error("No language columns found in CSV")
            return False
        
        logger.info(f"Found language columns: {language_columns}")
        logger.info(f"Found normalized columns: {normalized_columns}")
        
        return True
    
    def _transform_row_to_normalized(self, row: pd.Series) -> tuple:
        """
        Transform a CSV row into normalized schema format.

        Args:
            row: Pandas Series representing a CSV row

        Returns:
            Tuple of (chapter_code, term_code, translations)
        """
        # Extract chapter and term codes - handle non-integer values gracefully
        chapter_code = str(row['chapter_code']).strip()
        term_code = str(row['term_code']).strip()
        
        # Build translations dictionary
        translations = {}
        
        for lang in self.supported_languages:
            lang_data = {}
            
            # Get raw text
            if lang in row and pd.notna(row[lang]) and str(row[lang]).strip():
                lang_data['raw'] = str(row[lang]).strip()
            
            # Get normalized text
            normalized_col = f"{lang}_normalized"
            if normalized_col in row and pd.notna(row[normalized_col]) and str(row[normalized_col]).strip():
                lang_data['normalized'] = str(row[normalized_col]).strip()
            elif lang_data.get('raw'):
                # Use raw text as normalized if no normalized version available
                lang_data['normalized'] = lang_data['raw']
            
            # Only include language if it has content
            if lang_data.get('raw'):
                translations[lang] = lang_data
        
        return chapter_code, term_code, translations
    
    def get_loading_summary(self) -> Dict[str, Any]:
        """Get summary of loaded data."""
        return self.schema_manager.get_schema_stats()
    
    def search_loaded_terms(self, search_text: str, language_code: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Search terms in loaded data with advanced search capabilities."""
        return self.schema_manager.search_terms(search_text, language_code, limit)
    
    def close(self):
        """Close database connection."""
        self.schema_manager.close()


def find_icd11_csv_files(directory: str = ".") -> List[str]:
    """Find CSV files that start with 'icd11' in the given directory."""
    csv_files = []
    
    for file in os.listdir(directory):
        if file.lower().startswith('icd11') and file.lower().endswith('.csv'):
            csv_files.append(os.path.join(directory, file))
    
    return csv_files


def main():
    """Main function to load CSV data using improved schema."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Find ICD-11 CSV files
        csv_files = find_icd11_csv_files()
        
        if not csv_files:
            print("No ICD-11 CSV files found (files starting with 'icd11')")
            return
        
        print(f"Found {len(csv_files)} ICD-11 CSV file(s):")
        for i, file in enumerate(csv_files, 1):
            print(f"  {i}. {file}")
        
        # Use the first file found, or let user choose
        if len(csv_files) == 1:
            csv_file = csv_files[0]
            print(f"\nUsing: {csv_file}")
        else:
            try:
                choice = int(input(f"\nChoose file (1-{len(csv_files)}): ")) - 1
                csv_file = csv_files[choice]
            except (ValueError, IndexError):
                csv_file = csv_files[0]
                print(f"Invalid choice, using: {csv_file}")
        
        # Initialize loader
        loader = ImprovedCSVLoader()
        
        # Load CSV data
        print(f"\nLoading CSV data from: {csv_file}")
        results = loader.load_csv_to_database(
            csv_file, 
            create_schema=True, 
            drop_existing=True
        )
        
        # Display results
        if results['success']:
            print("\n✓ CSV loading completed successfully!")
            print(f"  Records processed: {results['records_processed']}")
            print(f"  Records inserted: {results['records_inserted']}")
            print(f"  Chapters created: {results['chapters_created']}")
            print(f"  Translations created: {results['translations_created']}")
            
            # Show schema summary
            summary = loader.get_loading_summary()
            print(f"\nDatabase Summary:")
            print(f"  Active chapters: {summary.get('active_chapters', 'N/A')}")
            print(f"  Active languages: {summary.get('active_languages', 'N/A')}")
            print(f"  Active terms: {summary.get('active_terms', 'N/A')}")
            print(f"  Total translations: {summary.get('total_translations', 'N/A')}")
            print(f"  Search index entries: {summary.get('search_index_entries', 'N/A')}")
            print(f"  Average quality score: {summary.get('avg_quality_score', 'N/A'):.3f}" if summary.get('avg_quality_score') else "  Average quality score: N/A")
            
            # Example search with improved capabilities
            print(f"\nExample advanced search (English 'infection'):")
            search_results = loader.search_loaded_terms('infection', 'en', 3)
            for i, result in enumerate(search_results, 1):
                relevance = result.get('relevance', 0)
                quality = result.get('quality_score', 0)
                print(f"  {i}. Chapter {result['chapter_code']}, Term {result['term_code']}: {result['raw_text']}")
                print(f"      (relevance: {relevance:.3f}, quality: {quality:.3f})")
        
        else:
            print("\n✗ CSV loading failed!")
            for error in results['errors']:
                print(f"  Error: {error}")
        
        loader.close()
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
