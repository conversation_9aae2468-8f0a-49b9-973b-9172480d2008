#!/usr/bin/env python3
"""
Verify Single Table Data
"""

from database_connection import DatabaseConnection

def verify_table():
    db = DatabaseConnection()
    db.connect()

    print('=== SINGLE TABLE VERIFICATION ===')
    print()

    # Check table count
    count = db.execute_query('SELECT COUNT(*) FROM icd11_terms;')[0]['count']
    print(f'✅ Total records in icd11_terms: {count}')

    print()
    print('=== TABLE STRUCTURE ===')

    # Show table structure
    columns = db.execute_query('''
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'icd11_terms' 
        ORDER BY ordinal_position;
    ''')

    for col in columns:
        col_name = col['column_name']
        data_type = col['data_type']
        print(f'  {col_name}: {data_type}')

    print()
    print('=== SAMPLE MULTILINGUAL DATA ===')

    # Show one complete record with all languages
    sample = db.execute_query('''
        SELECT chapter_code, term_code, en, es, fr, ar, de, uk
        FROM icd11_terms 
        LIMIT 1;
    ''')

    if sample:
        row = sample[0]
        print(f'Chapter: {row["chapter_code"]}')
        print(f'Term: {row["term_code"]}')
        print(f'EN: {row["en"]}')
        print(f'ES: {row["es"]}')
        print(f'FR: {row["fr"]}')
        print(f'AR: {row["ar"]}')
        print(f'DE: {row["de"]}')
        print(f'UK: {row["uk"]}')

    print()
    print('=== LANGUAGE COVERAGE ===')

    # Check how many records have each language
    languages = ['en', 'es', 'fr', 'ar', 'de', 'uk']
    for lang in languages:
        count_sql = f'SELECT COUNT(*) FROM icd11_terms WHERE {lang} IS NOT NULL AND {lang} != \'\';'
        count = db.execute_query(count_sql)[0]['count']
        print(f'{lang.upper()}: {count} records')

    print()
    print('=== SEARCH EXAMPLE ===')
    
    # Example search
    search_results = db.execute_query('''
        SELECT chapter_code, term_code, en, es 
        FROM icd11_terms 
        WHERE en ILIKE '%infection%' 
        LIMIT 3;
    ''')
    
    print('Terms containing "infection":')
    for row in search_results:
        print(f'  {row["chapter_code"]}.{row["term_code"]}: {row["en"][:60]}...')

    db.disconnect()

if __name__ == "__main__":
    verify_table()
